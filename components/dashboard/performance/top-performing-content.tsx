"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Eye, Heart, MessageSquare, Share2, ExternalLink } from "lucide-react"

const contentData = [
  {
    id: 1,
    title: "5 Social Media Trends for 2024",
    type: "Video",
    platform: "Instagram",
    date: "2024-01-15",
    reach: 125000,
    engagement: 8.2,
    likes: 8900,
    comments: 456,
    shares: 1200,
    score: 95,
  },
  {
    id: 2,
    title: "Behind the Scenes: Product Development",
    type: "Story",
    platform: "Instagram",
    date: "2024-01-20",
    reach: 89000,
    engagement: 7.8,
    likes: 6200,
    comments: 234,
    shares: 890,
    score: 92,
  },
  {
    id: 3,
    title: "Customer Success Spotlight",
    type: "Post",
    platform: "LinkedIn",
    date: "2024-01-22",
    reach: 67000,
    engagement: 6.5,
    likes: 3400,
    comments: 189,
    shares: 567,
    score: 88,
  },
  {
    id: 4,
    title: "Industry Report: Q1 Insights",
    type: "Article",
    platform: "LinkedIn",
    date: "2024-01-25",
    reach: 78000,
    engagement: 5.9,
    likes: 2800,
    comments: 145,
    shares: 890,
    score: 85,
  },
  {
    id: 5,
    title: "Quick Tips Tuesday",
    type: "Carousel",
    platform: "Instagram",
    date: "2024-01-28",
    reach: 56000,
    engagement: 7.1,
    likes: 3200,
    comments: 98,
    shares: 445,
    score: 82,
  },
]

export function TopPerformingContent() {
  const [sortBy, setSortBy] = useState("score")
  const [timeframe, setTimeframe] = useState("30days")

  const sortedContent = [...contentData].sort((a, b) => {
    if (sortBy === "score") return b.score - a.score
    if (sortBy === "engagement") return b.engagement - a.engagement
    if (sortBy === "reach") return b.reach - a.reach
    return 0
  })

  const getScoreColor = (score: number) => {
    if (score >= 90) return "bg-emerald-500"
    if (score >= 80) return "bg-blue-500"
    if (score >= 70) return "bg-orange-500"
    return "bg-red-500"
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
          <div>
            <CardTitle>Top Performing Content</CardTitle>
            <CardDescription>Your best content ranked by performance score</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={timeframe} onValueChange={setTimeframe}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7days">Last 7 Days</SelectItem>
                <SelectItem value="30days">Last 30 Days</SelectItem>
                <SelectItem value="90days">Last 90 Days</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="score">Performance Score</SelectItem>
                <SelectItem value="engagement">Engagement Rate</SelectItem>
                <SelectItem value="reach">Reach</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Content</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Date</TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <Eye className="mr-1 h-4 w-4" />
                    Reach
                  </div>
                </TableHead>
                <TableHead className="text-right">Engagement</TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <Heart className="mr-1 h-4 w-4" />
                    Likes
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <MessageSquare className="mr-1 h-4 w-4" />
                    Comments
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <Share2 className="mr-1 h-4 w-4" />
                    Shares
                  </div>
                </TableHead>
                <TableHead className="text-right">Score</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedContent.map((content) => (
                <TableRow key={content.id}>
                  <TableCell>
                    <div className="font-medium">{content.title}</div>
                    <div className="text-sm text-muted-foreground">
                      <Badge variant="outline" className="mr-1">
                        {content.type}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>{content.platform}</TableCell>
                  <TableCell>{content.date}</TableCell>
                  <TableCell className="text-right">{content.reach.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{content.engagement}%</TableCell>
                  <TableCell className="text-right">{content.likes.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{content.comments.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{content.shares.toLocaleString()}</TableCell>
                  <TableCell className="text-right">
                    <Badge className={`${getScoreColor(content.score)} text-white`}>{content.score}</Badge>
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
