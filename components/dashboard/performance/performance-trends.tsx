"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis, Line, LineChart } from "recharts"
import { ChartTooltip } from "@/components/ui/chart"

const trendsData = [
  { date: "Jan 1", reach: 45000, engagement: 3.2, clicks: 890, followers: 12450 },
  { date: "Jan 8", reach: 52000, engagement: 3.8, clicks: 1120, followers: 12680 },
  { date: "Jan 15", reach: 48000, engagement: 4.1, clicks: 980, followers: 12890 },
  { date: "Jan 22", reach: 61000, engagement: 4.5, clicks: 1340, followers: 13120 },
  { date: "Jan 29", reach: 58000, engagement: 4.2, clicks: 1250, followers: 13380 },
  { date: "Feb 5", reach: 67000, engagement: 4.8, clicks: 1580, followers: 13650 },
  { date: "Feb 12", reach: 72000, engagement: 5.1, clicks: 1720, followers: 13920 },
  { date: "Feb 19", reach: 69000, engagement: 4.9, clicks: 1650, followers: 14200 },
  { date: "Feb 26", reach: 78000, engagement: 5.3, clicks: 1890, followers: 14520 },
  { date: "Mar 5", reach: 82000, engagement: 5.6, clicks: 2010, followers: 14850 },
]

export function PerformanceTrends() {
  const [timeframe, setTimeframe] = useState("3months")
  const [activeTab, setActiveTab] = useState("reach")

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Performance Trends</CardTitle>
            <CardDescription>Track your metrics over time</CardDescription>
          </div>
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1month">Last Month</SelectItem>
              <SelectItem value="3months">Last 3 Months</SelectItem>
              <SelectItem value="6months">Last 6 Months</SelectItem>
              <SelectItem value="1year">Last Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="reach">Reach</TabsTrigger>
            <TabsTrigger value="engagement">Engagement</TabsTrigger>
            <TabsTrigger value="clicks">Clicks</TabsTrigger>
            <TabsTrigger value="followers">Followers</TabsTrigger>
          </TabsList>
          <TabsContent value="reach">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={trendsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip content={<ChartTooltip />} />
                <Area type="monotone" dataKey="reach" stroke="#8884d8" fill="#8884d8" fillOpacity={0.3} />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="engagement">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={trendsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip content={<ChartTooltip />} />
                <Line type="monotone" dataKey="engagement" stroke="#82ca9d" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="clicks">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={trendsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip content={<ChartTooltip />} />
                <Area type="monotone" dataKey="clicks" stroke="#ffc658" fill="#ffc658" fillOpacity={0.3} />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="followers">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={trendsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip content={<ChartTooltip />} />
                <Line type="monotone" dataKey="followers" stroke="#ff8042" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
