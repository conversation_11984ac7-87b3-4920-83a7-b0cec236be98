"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Lightbulb, TrendingUp, <PERSON>ert<PERSON><PERSON>gle, CheckCircle, ArrowRight } from "lucide-react"

export function PerformanceInsights() {
  const insights = [
    {
      type: "opportunity",
      title: "Video Content Performing 40% Better",
      description: "Your video posts are generating significantly higher engagement rates compared to static images.",
      impact: "high",
      action: "Create more video content",
      icon: TrendingUp,
    },
    {
      type: "alert",
      title: "Facebook Engagement Declining",
      description: "Facebook engagement has dropped 15% over the past month. Consider adjusting your content strategy.",
      impact: "medium",
      action: "Review Facebook strategy",
      icon: AlertTriangle,
    },
    {
      type: "success",
      title: "LinkedIn Growth Exceeding Goals",
      description: "Your LinkedIn follower growth is 25% ahead of your quarterly target.",
      impact: "high",
      action: "Maintain current strategy",
      icon: CheckCircle,
    },
    {
      type: "tip",
      title: "Optimal Posting Time Identified",
      description: "Posts published between 2-4 PM on weekdays receive 30% more engagement.",
      impact: "medium",
      action: "Adjust posting schedule",
      icon: Lightbulb,
    },
  ]

  const getInsightColor = (type: string) => {
    switch (type) {
      case "opportunity":
        return "border-l-blue-500 bg-blue-50"
      case "alert":
        return "border-l-orange-500 bg-orange-50"
      case "success":
        return "border-l-emerald-500 bg-emerald-50"
      case "tip":
        return "border-l-purple-500 bg-purple-50"
      default:
        return "border-l-gray-500 bg-gray-50"
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-orange-100 text-orange-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Performance Insights</CardTitle>
        <CardDescription>AI-powered recommendations to improve your performance</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {insights.map((insight, index) => (
            <div key={index} className={`rounded-lg border-l-4 p-4 ${getInsightColor(insight.type)}`}>
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <insight.icon className="mt-0.5 h-5 w-5" />
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{insight.title}</h4>
                      <Badge variant="outline" className={getImpactColor(insight.impact)}>
                        {insight.impact} impact
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{insight.description}</p>
                    <Button variant="outline" size="sm" className="mt-2">
                      {insight.action}
                      <ArrowRight className="ml-2 h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
