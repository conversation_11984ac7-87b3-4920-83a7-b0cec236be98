"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis, Bar, Bar<PERSON>hart } from "recharts"
import { ChartTooltip } from "@/components/ui/chart"

const hourlyData = [
  { hour: "00", activity: 12 },
  { hour: "01", activity: 8 },
  { hour: "02", activity: 5 },
  { hour: "03", activity: 3 },
  { hour: "04", activity: 4 },
  { hour: "05", activity: 8 },
  { hour: "06", activity: 15 },
  { hour: "07", activity: 25 },
  { hour: "08", activity: 35 },
  { hour: "09", activity: 45 },
  { hour: "10", activity: 52 },
  { hour: "11", activity: 58 },
  { hour: "12", activity: 65 },
  { hour: "13", activity: 72 },
  { hour: "14", activity: 78 },
  { hour: "15", activity: 82 },
  { hour: "16", activity: 75 },
  { hour: "17", activity: 68 },
  { hour: "18", activity: 62 },
  { hour: "19", activity: 58 },
  { hour: "20", activity: 52 },
  { hour: "21", activity: 45 },
  { hour: "22", activity: 35 },
  { hour: "23", activity: 22 },
]

const dailyData = [
  { day: "Mon", activity: 85 },
  { day: "Tue", activity: 92 },
  { day: "Wed", activity: 78 },
  { day: "Thu", activity: 88 },
  { day: "Fri", activity: 95 },
  { day: "Sat", activity: 72 },
  { day: "Sun", activity: 68 },
]

export function AudienceActivity() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Audience Activity</CardTitle>
        <CardDescription>When your audience is most active</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="hourly">
          <TabsList className="mb-4">
            <TabsTrigger value="hourly">Hourly</TabsTrigger>
            <TabsTrigger value="daily">Daily</TabsTrigger>
          </TabsList>
          <TabsContent value="hourly">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={hourlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip content={<ChartTooltip />} />
                <Area type="monotone" dataKey="activity" stroke="#8884d8" fill="#8884d8" fillOpacity={0.3} />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="daily">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={dailyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip content={<ChartTooltip />} />
                <Bar dataKey="activity" fill="#82ca9d" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
