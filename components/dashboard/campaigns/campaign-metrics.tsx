"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>Up, DollarSign, <PERSON><PERSON><PERSON><PERSON>, Target, Users } from "lucide-react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function CampaignMetrics() {
  const metrics = [
    {
      title: "Total Campaigns",
      value: "12",
      change: "+2",
      trend: "up",
      icon: Target,
    },
    {
      title: "Total Spend",
      value: "$24,892",
      change: "+8.2%",
      trend: "up",
      icon: DollarSign,
    },
    {
      title: "Total Clicks",
      value: "142,568",
      change: "+12.3%",
      trend: "up",
      icon: MousePointer,
    },
    {
      title: "Conversion Rate",
      value: "3.2%",
      change: "-0.4%",
      trend: "down",
      icon: Users,
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
            <metric.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <div className="flex items-center text-xs">
              {metric.trend === "up" ? (
                <ArrowUp className="mr-1 h-3 w-3 text-emerald-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-rose-500" />
              )}
              <span className={metric.trend === "up" ? "text-emerald-500" : "text-rose-500"}>{metric.change}</span>
              <span className="ml-1 text-muted-foreground">vs last period</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
