"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Funnel,
  FunnelChart,
  Legend,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

const funnelData = [
  {
    name: "Impressions",
    value: 12500,
    fill: "#4f46e5",
  },
  {
    name: "Clicks",
    value: 5200,
    fill: "#8b5cf6",
  },
  {
    name: "Leads",
    value: 2100,
    fill: "#a855f7",
  },
  {
    name: "Qualified Leads",
    value: 850,
    fill: "#d946ef",
  },
  {
    name: "Opportunities",
    value: 320,
    fill: "#ec4899",
  },
  {
    name: "Customers",
    value: 120,
    fill: "#f43f5e",
  },
]

const leadsBySourceData = [
  { name: "Facebook", value: 35 },
  { name: "Instagram", value: 25 },
  { name: "LinkedIn", value: 20 },
  { name: "Twitter", value: 10 },
  { name: "YouTube", value: 10 },
]

const COLORS = ["#4f46e5", "#8b5cf6", "#a855f7", "#d946ef", "#ec4899"]

const leadTrendsData = [
  { name: "Jan", facebook: 40, instagram: 24, linkedin: 35 },
  { name: "Feb", facebook: 30, instagram: 28, linkedin: 32 },
  { name: "Mar", facebook: 45, instagram: 32, linkedin: 37 },
  { name: "Apr", facebook: 50, instagram: 35, linkedin: 40 },
  { name: "May", facebook: 55, instagram: 40, linkedin: 45 },
  { name: "Jun", facebook: 60, instagram: 45, linkedin: 50 },
  { name: "Jul", facebook: 65, instagram: 50, linkedin: 55 },
]

const conversionRateData = [
  { name: "Mon", rate: 2.5 },
  { name: "Tue", rate: 3.1 },
  { name: "Wed", rate: 3.8 },
  { name: "Thu", rate: 3.3 },
  { name: "Fri", rate: 3.6 },
  { name: "Sat", rate: 2.8 },
  { name: "Sun", rate: 2.4 },
]

export function LeadGenerationSection() {
  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>Lead Generation</CardTitle>
        <CardDescription>Track your lead generation performance across platforms</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="funnel">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="funnel">Funnel</TabsTrigger>
            <TabsTrigger value="sources">Sources</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="conversion">Conversion</TabsTrigger>
          </TabsList>
          <TabsContent value="funnel" className="space-y-4">
            <div className="h-[300px] w-full pt-4">
              <ResponsiveContainer width="100%" height="100%">
                <FunnelChart>
                  <Tooltip
                    formatter={(value) => [`${value} users`, null]}
                    labelFormatter={(name) => `Stage: ${name}`}
                  />
                  <Funnel dataKey="value" data={funnelData} isAnimationActive>
                    {funnelData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.fill} />
                    ))}
                  </Funnel>
                </FunnelChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-3 gap-4 text-center">
              {funnelData.map((stage, index) => (
                <div key={index} className="rounded-lg border p-2">
                  <div className="text-sm font-medium">{stage.name}</div>
                  <div className="text-lg font-bold">{stage.value.toLocaleString()}</div>
                  {index < funnelData.length - 1 && (
                    <div className="text-xs text-muted-foreground">
                      {Math.round((funnelData[index + 1].value / stage.value) * 100)}% conversion
                    </div>
                  )}
                </div>
              ))}
            </div>
          </TabsContent>
          <TabsContent value="sources">
            <div className="h-[350px] w-full pt-4">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={leadsBySourceData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {leadsBySourceData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} leads`, null]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 grid grid-cols-5 gap-2">
              {leadsBySourceData.map((source, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className="h-3 w-3 rounded-full" style={{ backgroundColor: COLORS[index % COLORS.length] }} />
                  <div className="text-xs font-medium">{source.name}</div>
                  <div className="text-xs text-muted-foreground">{source.value}%</div>
                </div>
              ))}
            </div>
          </TabsContent>
          <TabsContent value="trends">
            <div className="h-[350px] w-full pt-4">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={leadTrendsData}
                  margin={{
                    top: 10,
                    right: 30,
                    left: 0,
                    bottom: 0,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Area type="monotone" dataKey="facebook" stackId="1" stroke="#4f46e5" fill="#4f46e5" />
                  <Area type="monotone" dataKey="instagram" stackId="1" stroke="#8b5cf6" fill="#8b5cf6" />
                  <Area type="monotone" dataKey="linkedin" stackId="1" stroke="#a855f7" fill="#a855f7" />
                  <Legend />
                </AreaChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 grid grid-cols-3 gap-4">
              <div className="rounded-lg border p-2 text-center">
                <div className="text-sm font-medium">Total Leads</div>
                <div className="text-lg font-bold">2,853</div>
                <div className="text-xs text-green-500">↑ 12.5%</div>
              </div>
              <div className="rounded-lg border p-2 text-center">
                <div className="text-sm font-medium">Avg. Cost per Lead</div>
                <div className="text-lg font-bold">$4.28</div>
                <div className="text-xs text-red-500">↑ 2.3%</div>
              </div>
              <div className="rounded-lg border p-2 text-center">
                <div className="text-sm font-medium">Lead Quality Score</div>
                <div className="text-lg font-bold">7.6/10</div>
                <div className="text-xs text-green-500">↑ 0.8</div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="conversion">
            <div className="h-[350px] w-full pt-4">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={conversionRateData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value}%`, "Conversion Rate"]} />
                  <Legend />
                  <Bar dataKey="rate" name="Conversion Rate (%)" fill="#4f46e5" />
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 grid grid-cols-2 gap-4">
              <div className="rounded-lg border p-3">
                <div className="text-sm font-medium">Best Converting Day</div>
                <div className="flex items-end justify-between">
                  <div className="text-lg font-bold">Wednesday</div>
                  <div className="text-sm text-green-500">3.8%</div>
                </div>
              </div>
              <div className="rounded-lg border p-3">
                <div className="text-sm font-medium">Worst Converting Day</div>
                <div className="flex items-end justify-between">
                  <div className="text-lg font-bold">Sunday</div>
                  <div className="text-sm text-red-500">2.4%</div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
