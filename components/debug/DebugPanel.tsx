"use client"

import React, { useState, useEffect } from 'react'
import { logger, type LogEntry } from '@/lib/debug/logger'
import { getComponentDebugInfo, clearComponentDebugInfo } from '@/lib/debug/component-debug'
import { getApiDebugInfo, clearApiDebugInfo, getApiPerformanceMetrics } from '@/lib/debug/api-debug'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import {
  Bug,
  ChevronDown,
  ChevronUp,
  Trash2,
  Download,
  Activity,
  Network,
  Database,
  Clock,
  AlertTriangle,
  Info,
  X
} from 'lucide-react'

export function DebugPanel() {
  const [isOpen, setIsOpen] = useState(false)
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [componentInfo, setComponentInfo] = useState<any>(null)
  const [apiInfo, setApiInfo] = useState<any>(null)
  const [apiMetrics, setApiMetrics] = useState<any>(null)
  const [isClient, setIsClient] = useState(false)

  // Only show in development and on client side
  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!isClient || process.env.NODE_ENV !== 'development') return

    // Listen for new log entries
    const handleDebugLog = (event: CustomEvent) => {
      setLogs(prev => [event.detail, ...prev].slice(0, 100))
    }

    window.addEventListener('debug-log', handleDebugLog as EventListener)

    // Initial load
    setLogs(logger.getLogs())
    setComponentInfo(getComponentDebugInfo())
    setApiInfo(getApiDebugInfo())
    setApiMetrics(getApiPerformanceMetrics())

    // Refresh data less frequently to improve performance
    const interval = setInterval(() => {
      setComponentInfo(getComponentDebugInfo())
      setApiInfo(getApiDebugInfo())
      setApiMetrics(getApiPerformanceMetrics())
    }, 10000) // Changed from 2000ms to 10000ms (10 seconds)

    return () => {
      window.removeEventListener('debug-log', handleDebugLog as EventListener)
      clearInterval(interval)
    }
  }, [isClient])

  // Early return after all hooks have been called
  if (process.env.NODE_ENV !== 'development' || !isClient) {
    return null
  }

  const handleClearLogs = () => {
    logger.clearLogs()
    setLogs([])
  }

  const handleClearComponentInfo = () => {
    clearComponentDebugInfo()
    setComponentInfo(getComponentDebugInfo())
  }

  const handleClearApiInfo = () => {
    clearApiDebugInfo()
    setApiInfo(getApiDebugInfo())
    setApiMetrics(getApiPerformanceMetrics())
  }

  const handleExportLogs = () => {
    if (!isClient) return

    const data = {
      timestamp: new Date().toISOString(),
      logs: logs.slice(0, 50),
      componentInfo,
      apiInfo,
      apiMetrics,
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `debug-export-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  const getLogIcon = (level: string) => {
    switch (level) {
      case 'error': return <AlertTriangle className="h-3 w-3 text-red-500" />
      case 'warn': return <AlertTriangle className="h-3 w-3 text-yellow-500" />
      case 'info': return <Info className="h-3 w-3 text-blue-500" />
      case 'performance': return <Clock className="h-3 w-3 text-green-500" />
      default: return <Bug className="h-3 w-3 text-gray-500" />
    }
  }

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString()
  }

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          size="sm"
          className="rounded-full shadow-lg"
          title="Open Debug Panel"
        >
          <Bug className="h-4 w-4" />
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-[80vh]">
      <Card className="shadow-xl border-2">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bug className="h-4 w-4" />
              <CardTitle className="text-sm">Debug Panel</CardTitle>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleExportLogs}
                title="Export Debug Data"
              >
                <Download className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                title="Close Debug Panel"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
          <CardDescription className="text-xs">
            Development debugging tools
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs defaultValue="logs" className="w-full">
            <TabsList className="grid w-full grid-cols-3 text-xs">
              <TabsTrigger value="logs" className="text-xs">
                <Activity className="h-3 w-3 mr-1" />
                Logs
              </TabsTrigger>
              <TabsTrigger value="performance" className="text-xs">
                <Clock className="h-3 w-3 mr-1" />
                Performance
              </TabsTrigger>
              <TabsTrigger value="network" className="text-xs">
                <Network className="h-3 w-3 mr-1" />
                Network
              </TabsTrigger>
            </TabsList>

            <TabsContent value="logs" className="p-4 pt-2">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs font-medium">Recent Logs ({logs.length})</span>
                <Button variant="ghost" size="sm" onClick={handleClearLogs}>
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              <ScrollArea className="h-64">
                <div className="space-y-1">
                  {logs.slice(0, 20).map((log, index) => (
                    <Collapsible key={index}>
                      <CollapsibleTrigger asChild>
                        <div className="flex items-start gap-2 p-2 rounded text-xs hover:bg-muted cursor-pointer">
                          {getLogIcon(log.level)}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-mono text-xs text-muted-foreground">
                                {formatTime(new Date(log.timestamp).toISOString())}
                              </span>
                              {log.source && (
                                <Badge variant="outline" className="text-xs px-1 py-0">
                                  {log.source}
                                </Badge>
                              )}
                            </div>
                            <p className="truncate">{log.message}</p>
                          </div>
                          <ChevronDown className="h-3 w-3" />
                        </div>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="px-4 pb-2">
                        {log.data && (
                          <pre className="text-xs bg-muted p-2 rounded overflow-x-auto">
                            {JSON.stringify(log.data, null, 2)}
                          </pre>
                        )}
                      </CollapsibleContent>
                    </Collapsible>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="performance" className="p-4 pt-2">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium">Components</span>
                  <Button variant="ghost" size="sm" onClick={handleClearComponentInfo}>
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
                
                {componentInfo && (
                  <div className="space-y-2">
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div className="bg-muted p-2 rounded text-center">
                        <div className="font-semibold">{componentInfo.totalComponents}</div>
                        <div className="text-muted-foreground">Components</div>
                      </div>
                      <div className="bg-muted p-2 rounded text-center">
                        <div className="font-semibold">{componentInfo.totalInstances}</div>
                        <div className="text-muted-foreground">Instances</div>
                      </div>
                      <div className="bg-muted p-2 rounded text-center">
                        <div className="font-semibold">{componentInfo.totalRenders}</div>
                        <div className="text-muted-foreground">Renders</div>
                      </div>
                    </div>
                    
                    <ScrollArea className="h-32">
                      <div className="space-y-1">
                        {componentInfo.components && Array.isArray(componentInfo.components) ? (
                          componentInfo.components.map((comp: any, index: number) => (
                            <div key={index} className="flex justify-between items-center p-2 bg-muted rounded text-xs">
                              <span className="font-mono">{comp.name || 'Unknown'}</span>
                              <div className="flex gap-2">
                                <Badge variant="outline" className="text-xs">
                                  {comp.instances || 0}i
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  {comp.renders || 0}r
                                </Badge>
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="text-xs text-muted-foreground p-2">
                            No component data available
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="network" className="p-4 pt-2">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium">API Requests</span>
                  <Button variant="ghost" size="sm" onClick={handleClearApiInfo}>
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
                
                {apiMetrics && (
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-muted p-2 rounded text-center">
                      <div className="font-semibold">{apiMetrics.totalRequests}</div>
                      <div className="text-muted-foreground">Total</div>
                    </div>
                    <div className="bg-muted p-2 rounded text-center">
                      <div className="font-semibold">{apiMetrics.averageResponseTime.toFixed(0)}ms</div>
                      <div className="text-muted-foreground">Avg Time</div>
                    </div>
                    <div className="bg-muted p-2 rounded text-center">
                      <div className="font-semibold">{apiMetrics.successRate.toFixed(1)}%</div>
                      <div className="text-muted-foreground">Success</div>
                    </div>
                    <div className="bg-muted p-2 rounded text-center">
                      <div className="font-semibold">{apiMetrics.errorRate.toFixed(1)}%</div>
                      <div className="text-muted-foreground">Errors</div>
                    </div>
                  </div>
                )}
                
                {apiInfo && (
                  <ScrollArea className="h-32">
                    <div className="space-y-1">
                      {apiInfo.recentResponses && Array.isArray(apiInfo.recentResponses) ? (
                        apiInfo.recentResponses.slice(0, 10).map((response: any, index: number) => (
                          <div key={index} className="flex justify-between items-center p-2 bg-muted rounded text-xs">
                            <div className="flex-1 min-w-0">
                              <div className="truncate font-mono">{response.url || 'Unknown URL'}</div>
                              <div className="text-muted-foreground">
                                {response.timestamp ? formatTime(new Date(response.timestamp).toISOString()) : 'Unknown time'}
                              </div>
                            </div>
                            <div className="flex gap-1">
                              <Badge
                                variant={response.status >= 400 ? "destructive" : "default"}
                                className="text-xs"
                              >
                                {response.status || 'N/A'}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {response.duration ? response.duration.toFixed(0) : '0'}ms
                              </Badge>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-xs text-muted-foreground p-2">
                          No API data available
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
