"use client"

import { Suspense, lazy, memo } from "react"

// Generic lazy chart wrapper
const Lazy<PERSON>hart = lazy(() => import("recharts").then(module => ({
  default: memo(({ type, data, config, ...props }: any) => {
    const { ResponsiveContainer } = module
    const ChartComponent = module[type]
    
    if (!ChartComponent) {
      throw new Error(`Chart type "${type}" not found in recharts`)
    }
    
    return (
      <ResponsiveContainer width="100%" height="100%">
        <ChartComponent data={data} {...config} {...props}>
          {config.children}
        </ChartComponent>
      </ResponsiveContainer>
    )
  })
})))

const ChartSkeleton = memo(function ChartSkeleton({ height = 350 }: { height?: number }) {
  return (
    <div 
      className="w-full bg-muted animate-pulse rounded flex items-center justify-center"
      style={{ height }}
    >
      <div className="text-muted-foreground text-sm">Loading chart...</div>
    </div>
  )
})

interface LazyChartWrapperProps {
  type: string
  data: any[]
  config?: any
  height?: number
  [key: string]: any
}

export function LazyChartWrapper({ 
  type, 
  data, 
  config, 
  height = 350, 
  ...props 
}: LazyChartWrapperProps) {
  return (
    <div className="w-full" style={{ height }}>
      <Suspense fallback={<ChartSkeleton height={height} />}>
        <LazyChart type={type} data={data} config={config} {...props} />
      </Suspense>
    </div>
  )
}

export { ChartSkeleton }
