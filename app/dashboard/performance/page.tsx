import type { Metadata } from "next"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { PerformanceHeader } from "@/components/dashboard/performance/performance-header"
import { PerformanceOverview } from "@/components/dashboard/performance/performance-overview"
import { PerformanceTrends } from "@/components/dashboard/performance/performance-trends"
import { TopPerformingContent } from "@/components/dashboard/performance/top-performing-content"
import { PlatformPerformance } from "@/components/dashboard/performance/platform-performance"
import { PerformanceGoals } from "@/components/dashboard/performance/performance-goals"
import { PerformanceInsights } from "@/components/dashboard/performance/performance-insights"

export const metadata: Metadata = {
  title: "Performance | Social Media Dashboard",
  description: "Detailed performance analytics and insights",
}

export default function PerformancePage() {
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        <PerformanceHeader />
        <PerformanceOverview />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <PerformanceTrends />
          <PerformanceGoals />
        </div>
        <TopPerformingContent />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <PlatformPerformance />
          <PerformanceInsights />
        </div>
      </div>
    </DashboardLayout>
  )
}
